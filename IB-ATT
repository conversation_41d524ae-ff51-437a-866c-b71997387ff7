//@version=5
indicator("Initial Balance-Advanced Time Technique", shorttitle="IB-ATT", overlay=true, max_bars_back=5000)

//----IB indicator -----
// Options
ib_session = input.session("0820-0920", title="Calculation period for the initial balance", group="Calculation period")

show_extra_levels = input.bool(true, "Show extra levels (IBH x2 & IBL x2)", group="Information")
show_intermediate_levels = input.bool(true, "Show intermediate levels (50%)", group="Information")
show_ib_calculation_area = input.bool(true, "Initial balance calculation period coloration", group="Information")
show_labels = input.bool(true, "Show labels", group="Information")
fill_ib_areas = input.bool(true, "Colour IB areas", group="Information")
only_current_levels = input.bool(false, "Only display the current IB Levels", group="Information")
only_current_zone = input.bool(false, "Only display the current IB calculation area", group="Information")



label_size = input.string("Small", title="Label Size", options=["Auto", "Huge", "Large", "Normal", "Small", "Tiny"], group="Drawings")
lvl_width = input.int(1, "Daily price level width", group="Drawings")
high_col = input.color(color.green, "Initial balance high levels color", group="Drawings")
low_col = input.color(color.red, "Initial balance low levels color", group="Drawings")
middle_col = input.color(#ffa726, "50% initial balance color", group="Drawings")
extend_level = input.string("Right", title="Extend current levels", options=["Right", "Left", "Both", "None"], group="Drawings")

main_levels_style = input.string("Solid" , "Main levels line style", options=["Solid", "Dashed", "Dotted"], group="Drawings")
ext_levels_style = input.string("Dashed" , "Extended levels line style", options=["Solid", "Dashed", "Dotted"], group="Drawings")
int_levels_style = input.string("Dotted" , "Intermediate levels line style", options=["Solid", "Dashed", "Dotted"], group="Drawings")

fill_ib_color= input.color(#b8851faa, "IB area background color", group="Drawings")
ext = extend_level == "Right" ? extend.right : extend_level == "Left" ? extend.left : extend_level == "Both" ? extend.both : extend.none

var delta_history = array.new_float(20)

inSession(sess) => na(time(timeframe.period, sess)) == false

get_line_style(s) =>
    s == "Solid" ? line.style_solid : s == "Dotted" ? line.style_dotted : line.style_dashed

get_levels(n) =>
    h = high[1]
    l = low[1]
    for i=1 to n
        if low[i] < l
            l := low[i]
        if high[i] > h
            h := high[i]
    [h, l, (h+l)/2]

var line ibh = na
var line ibl = na
var line ibm = na
var line ib_plus = na
var line ib_minus = na
var line ib_plus2 = na
var line ib_minus2 = na
var line ibm_plus = na
var line ibm_minus = na

var label labelh = na
var label labell = na
var label labelm = na
var label label_plus = na
var label label_minus = na
var label label_plus2 = na
var label label_minus2 = na
var label labelm_plus = na
var label labelm_minus = na
var box ib_area = na

labelSize = (label_size == "Huge") ? size.huge :
     (label_size == "Large") ? size.large :
     (label_size == "Small") ? size.small :
     (label_size == "Tiny") ? size.tiny :
     (label_size == "Auto") ? size.auto : size.normal

var offset = 0

ins = inSession(ib_session)

bgcolor(show_ib_calculation_area and ins ? #673ab730 : na, title="IB calculation zone")
var float ib_delta = na

if ins
    offset += 1
if ins[1] and not ins
    [h, l, m] = get_levels(offset)
    ib_delta := h - l
    if array.size(delta_history) >= 20
        array.shift(delta_history)
    array.push(delta_history, ib_delta)
    
    line.set_extend(ibh, extend.none)
    line.set_extend(ibl, extend.none)
    if show_intermediate_levels
        line.set_extend(ibm, extend.none)
    if show_extra_levels
        line.set_extend(ib_plus, extend.none)
        line.set_extend(ib_minus, extend.none)
        line.set_extend(ib_plus2, extend.none)
        line.set_extend(ib_minus2, extend.none)
        if show_intermediate_levels
            line.set_extend(ibm_plus, extend.none)
            line.set_extend(ibm_minus, extend.none)
    if show_labels
        if only_current_levels
            label.delete(labelh)
            label.delete(labell)
            label.delete(labelm)
            label.delete(label_plus)
            label.delete(label_minus)
            label.delete(label_plus2)
            label.delete(label_minus2)
            label.delete(labelm_plus)
            label.delete(labelm_minus)
        labelh := label.new(bar_index[offset], h, text="IBH 100%: "+str.tostring(h), style=label.style_none, textcolor=high_col, size=labelSize)
        labell := label.new(bar_index[offset], l, text="IBL 0%: "+str.tostring(l), style=label.style_none, textcolor=low_col, size=labelSize)
        if show_intermediate_levels
            labelm := label.new(bar_index[offset], m, text="IBM 50%: "+str.tostring(m)+"\nIBΔ: "+str.tostring(h - l), style=label.style_none, textcolor=middle_col, size=labelSize)
        if show_extra_levels
            label_plus := label.new(bar_index[offset], h + ib_delta, text="IBH x2 - "+str.tostring(h + ib_delta), style=label.style_none, textcolor=high_col, size=labelSize)
            label_minus := label.new(bar_index[offset], l - ib_delta, text="IBL x2: "+str.tostring(l - ib_delta), style=label.style_none, textcolor=low_col, size=labelSize)
            label_plus2 := label.new(bar_index[offset], h + (ib_delta*2), text="IBH x3 - "+str.tostring(h + (ib_delta*2)), style=label.style_none, textcolor=high_col, size=labelSize)
            label_minus2 := label.new(bar_index[offset], l - (ib_delta*2), text="IBL x3: "+str.tostring(l - (ib_delta*2)), style=label.style_none, textcolor=low_col, size=labelSize)
    if fill_ib_areas
        if only_current_zone
            box.delete(ib_area)
        ib_area := box.new(bar_index[offset], h, bar_index, l, bgcolor=fill_ib_color, border_color=#00000000)//, extend=ext)
    if only_current_levels
        line.delete(ibh)
        line.delete(ibl)
        line.delete(ibm)
        line.delete(ib_plus)
        line.delete(ib_minus)
        line.delete(ib_plus2)
        line.delete(ib_minus2)
        line.delete(ibm_plus)
        line.delete(ibm_minus)
     
    ibh := line.new(bar_index[offset], h, bar_index, h, color=high_col, extend=ext, width=lvl_width, style=get_line_style(main_levels_style))
    ibl := line.new(bar_index[offset], l, bar_index, l, color=low_col, extend=ext, width=lvl_width, style=get_line_style(main_levels_style))
    if show_intermediate_levels
        ibm := line.new(bar_index[offset], m, bar_index, m, color=middle_col, style=get_line_style(int_levels_style), extend=ext, width=lvl_width)
    if show_extra_levels
        ib_plus := line.new(bar_index[offset], h + ib_delta, bar_index, h + ib_delta, color=high_col, style=get_line_style(ext_levels_style), extend=ext, width=lvl_width)
        ib_minus := line.new(bar_index[offset], l - ib_delta, bar_index, l - ib_delta, color=low_col, style=get_line_style(ext_levels_style), extend=ext, width=lvl_width)
        ib_plus2 := line.new(bar_index[offset], h + (ib_delta*2), bar_index, h + (ib_delta *2), color=high_col, style=get_line_style(ext_levels_style), extend=ext, width=lvl_width)
        ib_minus2 := line.new(bar_index[offset], l - (ib_delta*2), bar_index, l - (ib_delta*2), color=low_col, style=get_line_style(ext_levels_style), extend=ext, width=lvl_width)        
        if show_intermediate_levels
            ibm_plus := line.new(bar_index[offset], h + (ib_delta/2), bar_index, h + (ib_delta/2), color=middle_col, style=get_line_style(int_levels_style), extend=ext, width=lvl_width)
            ibm_minus := line.new(bar_index[offset], l - (ib_delta/2), bar_index, l - (ib_delta/2), color=middle_col, style=get_line_style(int_levels_style), extend=ext, width=lvl_width)
    offset := 0

if (not ins) and (not ins[1])
    line.set_x2(ibh, bar_index)
    line.set_x2(ibl, bar_index)
    if show_intermediate_levels
        line.set_x2(ibm, bar_index)
    if show_extra_levels    
        line.set_x2(ib_plus, bar_index)
        line.set_x2(ib_minus, bar_index)
        line.set_x2(ib_plus2, bar_index)
        line.set_x2(ib_minus2, bar_index)        
        if show_intermediate_levels
            line.set_x2(ibm_plus, bar_index)
            line.set_x2(ibm_minus, bar_index)

var table ib_analytics = table.new(position.bottom_left, 2, 6)

ib_sentiment() =>
    h = array.max(delta_history)
    l = array.min(delta_history)
    a = array.avg(delta_history)
    
    h_comp = ib_delta > h ? ib_delta - h : (ib_delta - h) * -1
    l_comp = ib_delta > l ? ib_delta - l : (ib_delta - l) * -1
    a_comp = ib_delta > a ? ib_delta - a : (ib_delta - a) * -1
    (h_comp < l_comp and h_comp < a_comp) ? "Huge" : (l_comp < h_comp and l_comp < a_comp) ? "Small" : "Medium"
    
//----IB indicator -----
//-----att indicator-----
// HTF Box Settings
group_candle = 'HTF Box Settings'

// HTF Box 1
htfCndl1  = input.bool(true, '1st HTF Box', inline='TYP1', group=group_candle)
htfUser1  = input.string('1 Hour', 'TF1', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP1', group=group_candle)
bullC1    = input.color(#26a69a, 'Bull1', inline='COL1', group=group_candle)
bearC1    = input.color(#ef5350, 'Bear1', inline='COL1', group=group_candle)



// Common Settings
group_common = 'Common Settings'
trans        = input.int(85, 'Transparency', inline='STYLE', minval=65, maxval=95, group=group_common)
lw           = input.int(1, 'Line Width', inline='STYLE', minval=1, maxval=4, group=group_common)
showNumbers  = input.bool(true, 'Show Candle Numbers', group=group_common)
numbersColor = input.color(color.white, 'Numbers Color', inline='NUM', group=group_common)
numbersSize  = input.string(size.small, 'Numbers Size', options=[size.tiny, size.small, size.normal], inline='NUM', group=group_common)

// ATT Circle Settings
group_att = 'ATT Circle Settings'
showATT      = input.bool(true, 'Show ATT Circles', group=group_att)
showATTNumbers = input.bool(true, 'Show ATT Numbers on Circles', group=group_att)
attColor1    = input.color(#8ee60ac4, 'HTF1 Color', inline='ATT_COL1', group=group_att)

// The ATT candle numbers where arrow marks will be drawn
var att_numbers = array.from(3, 11, 17, 29, 41, 47, 53, 59)

checkIf(_chartTF, _candlestickTF) =>
    var stat = false
    candlestickTF = _candlestickTF == 'D' ? 1440 : _candlestickTF == 'W' ? 10080 :  _candlestickTF == 'M' ? 302400 :  _candlestickTF == '3M' ? 3 * 302400 :  _candlestickTF == '6M' ? 6 * 302400 : _candlestickTF == '12M' ? 12 * 302400 : str.tonumber(_candlestickTF)
    if timeframe.isintraday
        stat := candlestickTF >= str.tonumber(_chartTF)
    else
        chartTF = str.contains(_chartTF, 'D') ? _chartTF == 'D' ? 1440 : str.tonumber(str.replace(_chartTF, 'D', '', 0)) * 1440 : str.contains(_chartTF, 'W') ? _chartTF == 'W' ? 10080 : str.tonumber(str.replace(_chartTF, 'W', '', 0)) * 10080 : _chartTF == 'M' ? 302400 : str.tonumber(str.replace(_chartTF, 'M', '', 0)) * 302400
        stat := candlestickTF >= chartTF
    stat

f_htf_ohlc(_htf) =>
    var htf_o  = 0., var htf_h  = 0., var htf_l  = 0., htf_c  = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.
    var int candleCount = 0

    if ta.change(time(_htf))
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        candleCount := 0
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low , htf_l)
        candleCount += 1
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c, candleCount]

f_getTF(_htf) =>
    _htf == '3 Mins' ? '3' :_htf == '5 Mins' ? '5' :_htf == '10 Mins' ? '10' :_htf == '15 Mins' ? '15' :_htf == '30 Mins' ? '30' :_htf == '45 Mins' ? '45' :_htf == '1 Hour' ? '60' :_htf == '2 Hours' ? '120' :_htf == '3 Hours' ? '180' :_htf == '4 Hours' ? '240' :_htf == '1 Day' ? 'D' :_htf == '1 Week' ? 'W' :_htf == '1 Month' ? 'M' :_htf == '3 Months' ? '3M' :_htf == '6 Months' ? '6M' :_htf == '1 Year' ? '12M' : na

// Global label arrays for each HTF box
var label[] candleLabelsHTF1 = array.new_label()

// Function to check if current bar should show ATT circle
f_checkATTCondition(_show, _htf) =>
    result = false
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        result := array.includes(att_numbers, currentCandle)
    result

// Function to get ATT number for current bar
f_getATTNumber(_show, _htf) =>
    attNumber = 0
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        if array.includes(att_numbers, currentCandle)
            attNumber := currentCandle
    attNumber



// Function to draw candle numbers independently
f_drawCandleNumbers(_show, _htf, _labelArr) =>
    if _show and showNumbers
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = f_htf_ohlc(_htf)
        labelPos = low - (high - low) * 0.5
        candleLabel = label.new(bar_index, labelPos, str.tostring(candleCount + 1), style=label.style_label_center, textcolor=numbersColor, size=numbersSize, yloc=yloc.price, color=na, textalign=text.align_center)
        array.push(_labelArr, candleLabel)

        // Cleanup labels to prevent overload
        if array.size(_labelArr) > 480
            label.delete(array.shift(_labelArr))

f_processCandles(_show, _htf, _bullC, _bearC, _trans, _width, _labelArr, _htfNumber) =>
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = f_htf_ohlc(_htf)

        color0  = O0 < C0 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color01 = O0 < C0 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)
        color1  = O1 < C1 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color11 = O1 < C1 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)

        var box hl  = na
        var box oc  = na
        var int x11 = na
        var int x1  = na

        if _htf != timeframe.period
            if ta.change(time(_htf))
                x11 := x1
                x1  := bar_index

                if L1 != 0
                    box.new(x11, H1, x1 - 1, L1, color11, _width, line.style_dotted, extend.none, xloc.bar_index, color1)
                    box.new(x11, O1, x1 - 1, C1, color11, _width, line.style_solid , extend.none, xloc.bar_index, color1)

                box.delete(hl), hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, color01, _width, line.style_dotted, extend.none, xloc.bar_index, color0)
                box.delete(oc), oc := box.new(x1, O0, 2 * x1 - x11 - 1, C0, color01, _width, line.style_solid , extend.none, xloc.bar_index, color0)

            else
                box.set_top(hl, H0)
                box.set_bottom(hl, L0)
                box.set_bgcolor(hl, color0)
                box.set_border_color(hl, color01)

                box.set_top(oc, math.max(O0, C0))
                box.set_bottom(oc, math.min(O0, C0))
                box.set_bgcolor(oc, color0)
                box.set_border_color(oc, color01)

            // Cleanup labels to prevent overload
            if array.size(_labelArr) > 480
                label.delete(array.shift(_labelArr))

// ------------------------- Main Logic ------------------------- //

// Process HTF Box 1
htf1 = f_getTF(htfUser1)
supported1 = checkIf(timeframe.period, htf1)



if chart.is_standard
    // Draw HTF Box 1
    if supported1
        f_processCandles(htfCndl1, htf1, bullC1, bearC1, trans, lw, candleLabelsHTF1, 1)

// Calculate ATT conditions for HTF timeframe
attCondition1 = supported1 ? f_checkATTCondition(htfCndl1, htf1) : false
attNumber1 = supported1 ? f_getATTNumber(htfCndl1, htf1) : 0

// Determine candle color for positioning
isRedCandle = close < open
isGreenCandle = close >= open

// Plot ATT circles using plotshape (must be in global scope for historical display)
// HTF1 - With numbers - Red candles (above bar)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 3 and isRedCandle, title="ATT HTF1-3 Red", style=shape.circle, location=location.abovebar, color=attColor1, size=size.small, text="3", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 11 and isRedCandle, title="ATT HTF1-11 Red", style=shape.circle, location=location.abovebar, color=attColor1, size=size.small, text="11", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 17 and isRedCandle, title="ATT HTF1-17 Red", style=shape.circle, location=location.abovebar, color=attColor1, size=size.small, text="17", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 29 and isRedCandle, title="ATT HTF1-29 Red", style=shape.circle, location=location.abovebar, color=attColor1, size=size.small, text="29", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 41 and isRedCandle, title="ATT HTF1-41 Red", style=shape.circle, location=location.abovebar, color=attColor1, size=size.small, text="41", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 47 and isRedCandle, title="ATT HTF1-47 Red", style=shape.circle, location=location.abovebar, color=attColor1, size=size.small, text="47", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 53 and isRedCandle, title="ATT HTF1-53 Red", style=shape.circle, location=location.abovebar, color=attColor1, size=size.small, text="53", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 59 and isRedCandle, title="ATT HTF1-59 Red", style=shape.circle, location=location.abovebar, color=attColor1, size=size.small, text="59", textcolor=color.white)

// HTF1 - With numbers - Green candles (below bar)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 3 and isGreenCandle, title="ATT HTF1-3 Green", style=shape.circle, location=location.belowbar, color=attColor1, size=size.small, text="3", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 11 and isGreenCandle, title="ATT HTF1-11 Green", style=shape.circle, location=location.belowbar, color=attColor1, size=size.small, text="11", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 17 and isGreenCandle, title="ATT HTF1-17 Green", style=shape.circle, location=location.belowbar, color=attColor1, size=size.small, text="17", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 29 and isGreenCandle, title="ATT HTF1-29 Green", style=shape.circle, location=location.belowbar, color=attColor1, size=size.small, text="29", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 41 and isGreenCandle, title="ATT HTF1-41 Green", style=shape.circle, location=location.belowbar, color=attColor1, size=size.small, text="41", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 47 and isGreenCandle, title="ATT HTF1-47 Green", style=shape.circle, location=location.belowbar, color=attColor1, size=size.small, text="47", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 53 and isGreenCandle, title="ATT HTF1-53 Green", style=shape.circle, location=location.belowbar, color=attColor1, size=size.small, text="53", textcolor=color.white)
plotshape(showATT and showATTNumbers and attCondition1 and attNumber1 == 59 and isGreenCandle, title="ATT HTF1-59 Green", style=shape.circle, location=location.belowbar, color=attColor1, size=size.small, text="59", textcolor=color.white)

// HTF1 - Without numbers
plotshape(showATT and not showATTNumbers and attCondition1 and isRedCandle, title="ATT HTF1 Red", style=shape.circle, location=location.abovebar, color=attColor1, size=size.small)
plotshape(showATT and not showATTNumbers and attCondition1 and isGreenCandle, title="ATT HTF1 Green", style=shape.circle, location=location.belowbar, color=attColor1, size=size.small)

// Draw candle numbers for the HTF timeframe
if supported1 and htfCndl1
    f_drawCandleNumbers(true, htf1, candleLabelsHTF1)


//-----att indicator-----